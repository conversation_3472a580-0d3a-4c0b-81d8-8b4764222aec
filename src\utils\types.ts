// 通用类型定义
export interface User {
  id: number;
  name: string;
  email: string;
  age?: number;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 环境配置类型
export interface AppConfig {
  title: string;
  apiBaseUrl: string;
  version: string;
}

// 工具函数
export function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN');
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 常量定义
export const APP_CONFIG: AppConfig = {
  title: 'Vue2 + TypeScript + Vite',
  apiBaseUrl: (import.meta as unknown as { env?: { VITE_API_BASE_URL?: string } }).env?.VITE_API_BASE_URL || '/api',
  version: '1.0.0'
};
