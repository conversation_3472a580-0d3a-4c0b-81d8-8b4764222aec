<template>
  <div id="app">
    <img alt="Vue logo" src="./assets/vue.svg" />
    <HelloWorld msg="Welcome to Your Vue.js + TypeScript + Vite App" />
  </div>
</template>

<script lang="ts">
  import Vue from 'vue';
  import HelloWorld from './components/HelloWorld.vue';

  interface AppData {
    // 可以在这里定义应用的数据类型
  }

  export default Vue.extend({
    name: 'App',
    components: {
      HelloWorld,
    },
    data(): AppData {
      return {
        // 应用数据
      };
    },
    mounted(): void {
      console.log('App component mounted');
    },
  });
</script>

<style>
  #app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    margin-top: 60px;
  }
</style>
