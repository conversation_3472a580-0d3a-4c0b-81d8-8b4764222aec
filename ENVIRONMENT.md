# 环境配置说明

本项目支持多环境配置，包括 TEST、UAT、PROD 三个环境。

## 📁 环境配置文件

### 测试环境 (.env.test)
```bash
NODE_ENV=test
VITE_APP_ENV=test
VITE_APP_TITLE=Vue2 Vite App (Test)
VITE_APP_API_BASE_URL=https://api-test.example.com
VITE_APP_API_TIMEOUT=10000
VITE_APP_DEBUG=true
VITE_APP_LOG_LEVEL=debug
VITE_APP_FEATURE_FLAGS={"newFeature":true,"betaMode":true}
```

### UAT环境 (.env.uat)
```bash
NODE_ENV=uat
VITE_APP_ENV=uat
VITE_APP_TITLE=Vue2 Vite App (UAT)
VITE_APP_API_BASE_URL=https://api-uat.example.com
VITE_APP_API_TIMEOUT=15000
VITE_APP_DEBUG=false
VITE_APP_LOG_LEVEL=info
VITE_APP_FEATURE_FLAGS={"newFeature":true,"betaMode":false}
```

### 生产环境 (.env.prod)
```bash
NODE_ENV=production
VITE_APP_ENV=prod
VITE_APP_TITLE=Vue2 Vite App
VITE_APP_API_BASE_URL=https://api.example.com
VITE_APP_API_TIMEOUT=20000
VITE_APP_DEBUG=false
VITE_APP_LOG_LEVEL=error
VITE_APP_FEATURE_FLAGS={"newFeature":false,"betaMode":false}
```

## 🚀 启动命令

### 开发模式
```bash
# 默认开发环境
npm run dev

# 测试环境开发
npm run dev:test

# UAT环境开发
npm run dev:uat

# 生产环境开发
npm run dev:prod
```

### 构建模式
```bash
# 默认构建
npm run build

# 测试环境构建
npm run build:test

# UAT环境构建
npm run build:uat

# 生产环境构建
npm run build:prod
```

### 预览模式
```bash
# 默认预览
npm run preview

# 测试环境预览
npm run preview:test

# UAT环境预览
npm run preview:uat

# 生产环境预览
npm run preview:prod
```

## 🔧 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `NODE_ENV` | Node.js 环境 | `test`, `uat`, `production` |
| `VITE_APP_ENV` | 应用环境标识 | `test`, `uat`, `prod` |
| `VITE_APP_TITLE` | 应用标题 | `Vue2 Vite App (Test)` |
| `VITE_APP_API_BASE_URL` | API 基础地址 | `https://api-test.example.com` |
| `VITE_APP_API_TIMEOUT` | API 请求超时时间(ms) | `10000` |
| `VITE_APP_DEBUG` | 调试模式开关 | `true`/`false` |
| `VITE_APP_LOG_LEVEL` | 日志级别 | `debug`, `info`, `error` |
| `VITE_APP_FEATURE_FLAGS` | 功能开关配置 | `{"newFeature":true}` |

## 💻 在代码中使用

### 导入环境配置
```javascript
import { env, getEnvInfo, isFeatureEnabled, getApiConfig } from '@/config/env.js';
```

### 获取环境信息
```javascript
// 获取完整环境信息
const envInfo = getEnvInfo();
console.log('当前环境:', envInfo.environment);
console.log('API地址:', envInfo.apiUrl);

// 直接访问环境变量
console.log('是否调试模式:', env.DEBUG);
console.log('是否测试环境:', env.isTest);
```

### 检查功能开关
```javascript
// 检查特定功能是否启用
if (isFeatureEnabled('newFeature')) {
  console.log('新功能已启用');
}

// 获取所有功能开关
console.log('功能开关:', env.FEATURE_FLAGS);
```

### 获取API配置
```javascript
// 获取API配置
const apiConfig = getApiConfig();
console.log('API基础地址:', apiConfig.baseURL);
console.log('API超时时间:', apiConfig.timeout);
```

## 🎯 环境特性对比

| 特性 | 测试环境 | UAT环境 | 生产环境 |
|------|----------|---------|----------|
| 调试模式 | ✅ 开启 | ❌ 关闭 | ❌ 关闭 |
| 日志级别 | debug | info | error |
| API超时 | 10s | 15s | 20s |
| 新功能 | ✅ 启用 | ✅ 启用 | ❌ 禁用 |
| Beta模式 | ✅ 启用 | ❌ 禁用 | ❌ 禁用 |

## 🔒 安全注意事项

1. **敏感信息**: 不要在环境文件中存储敏感信息（如密码、密钥）
2. **版本控制**: 环境配置文件已添加到 `.gitignore` 中，不会被提交到版本控制
3. **本地开发**: 使用 `.env.local` 文件进行本地开发配置
4. **CI/CD**: 在部署时通过环境变量注入敏感配置

## 📝 自定义环境

如需添加新的环境配置：

1. 创建新的环境文件，如 `.env.staging`
2. 在 `package.json` 中添加对应的脚本命令
3. 更新 `src/config/env.js` 中的环境判断逻辑
4. 更新 `src/components/EnvInfo.vue` 中的环境样式

## 🚨 故障排除

### 环境变量未生效
- 检查环境文件名是否正确
- 确认启动命令中包含了正确的 `--mode` 参数
- 重启开发服务器

### 功能开关解析失败
- 检查 `VITE_APP_FEATURE_FLAGS` 是否为有效的 JSON 格式
- 查看浏览器控制台的错误信息

### API 请求失败
- 确认 `VITE_APP_API_BASE_URL` 配置正确
- 检查网络连接和防火墙设置 