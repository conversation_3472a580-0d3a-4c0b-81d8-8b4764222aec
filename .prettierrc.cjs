module.exports = {
  // 基本配置
  semi: true,                    // 在语句末尾添加分号
  trailingComma: 'es5',          // 在对象和数组末尾添加逗号
  singleQuote: true,             // 使用单引号
  printWidth: 80,                // 每行最大长度
  tabWidth: 2,                   // 缩进宽度
  useTabs: false,                // 使用空格而不是制表符
  endOfLine: 'auto',             // 行尾换行符

  // Vue特定配置
  vueIndentScriptAndStyle: true, // Vue 文件中的 script 和 style 标签内容缩进

  // 对象和数组配置
  bracketSpacing: true,          // 对象字面量中的括号前后添加空格
  bracketSameLine: false,        // 将多行 JSX 元素的闭合标签放在新行
  arrowParens: 'avoid',          // 箭头函数参数周围省略括号

  // 字符串配置
  quoteProps: 'as-needed',       // 仅在必要时为对象属性添加引号
  jsxSingleQuote: true,          // JSX 中使用单引号

  // SCSS/CSS 相关配置
  htmlWhitespaceSensitivity: 'css', // CSS 空格敏感度，影响 HTML 中的空白处理
  embeddedLanguageFormatting: 'auto', // 自动格式化嵌入的语言（如 Vue 中的 SCSS）

  // 其他配置
  proseWrap: 'preserve',         // 保持 markdown 等文本的换行
};
