/**
 * 环境配置工具
 * 统一管理所有环境变量
 */

// 环境变量配置
export const env = {
  // 基础配置
  NODE_ENV: import.meta.env.NODE_ENV,
  APP_ENV: import.meta.env.VITE_APP_ENV || 'development',
  APP_TITLE: import.meta.env.VITE_APP_TITLE || 'Vue2 Vite App',

  // API 配置
  API_BASE_URL:
    import.meta.env.VITE_APP_API_BASE_URL || 'http://localhost:3000',
  API_TIMEOUT: parseInt(import.meta.env.VITE_APP_API_TIMEOUT) || 10000,

  // 调试配置
  DEBUG: import.meta.env.VITE_APP_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_APP_LOG_LEVEL || 'info',

  // 功能开关
  FEATURE_FLAGS: parseFeatureFlags(import.meta.env.VITE_APP_FEATURE_FLAGS),

  // 是否为开发环境
  isDev: import.meta.env.DEV,

  // 是否为生产环境
  isProd: import.meta.env.PROD,

  // 是否为测试环境
  isTest: import.meta.env.VITE_APP_ENV === 'test',

  // 是否为 UAT 环境
  isUat: import.meta.env.VITE_APP_ENV === 'uat',
};

/**
 * 解析功能开关配置
 * @param {string} flagsStr - 功能开关字符串
 * @returns {object} 功能开关对象
 */
function parseFeatureFlags(flagsStr) {
  try {
    return flagsStr ? JSON.parse(flagsStr) : {};
  } catch (error) {
    console.warn('Failed to parse feature flags:', error);
    return {};
  }
}

/**
 * 获取环境信息
 * @returns {object} 环境信息对象
 */
export function getEnvInfo() {
  return {
    environment: env.APP_ENV,
    title: env.APP_TITLE,
    apiUrl: env.API_BASE_URL,
    debug: env.DEBUG,
    logLevel: env.LOG_LEVEL,
    featureFlags: env.FEATURE_FLAGS,
  };
}

/**
 * 检查功能开关是否启用
 * @param {string} featureName - 功能名称
 * @returns {boolean} 是否启用
 */
export function isFeatureEnabled(featureName) {
  return env.FEATURE_FLAGS[featureName] === true;
}

/**
 * 获取 API 配置
 * @returns {object} API 配置对象
 */
export function getApiConfig() {
  return {
    baseURL: env.API_BASE_URL,
    timeout: env.API_TIMEOUT,
  };
}

// 默认导出
export default env;
