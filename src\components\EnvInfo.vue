<template>
  <div class="env-info">
    <h3>环境信息</h3>
    <div class="env-grid">
      <div class="env-item">
        <span class="label">环境:</span>
        <span class="value" :class="envClass">{{ envInfo.environment }}</span>
      </div>
      <div class="env-item">
        <span class="label">标题:</span>
        <span class="value">{{ envInfo.title }}</span>
      </div>
      <div class="env-item">
        <span class="label">API地址:</span>
        <span class="value">{{ envInfo.apiUrl }}</span>
      </div>
      <div class="env-item">
        <span class="label">调试模式:</span>
        <span class="value" :class="{ 'debug-on': envInfo.debug }">
          {{ envInfo.debug ? '开启' : '关闭' }}
        </span>
      </div>
      <div class="env-item">
        <span class="label">日志级别:</span>
        <span class="value">{{ envInfo.logLevel }}</span>
      </div>
    </div>

    <div
      class="feature-flags"
      v-if="Object.keys(envInfo.featureFlags).length > 0"
    >
      <h4>功能开关</h4>
      <div class="flags-grid">
        <div
          v-for="(enabled, feature) in envInfo.featureFlags"
          :key="feature"
          class="flag-item"
          :class="{ enabled: enabled }"
        >
          <span class="flag-name">{{ feature }}</span>
          <span class="flag-status">{{ enabled ? '启用' : '禁用' }}</span>
        </div>
      </div>
    </div>

    <div class="env-actions">
      <button @click="showEnvInfo" class="btn">显示环境详情</button>
      <button @click="checkFeature('newFeature')" class="btn">
        检查新功能
      </button>
    </div>
  </div>
</template>

<script>
  import { env, getEnvInfo, isFeatureEnabled } from '@/config/env.js';

  export default {
    name: 'EnvInfo',
    data() {
      return {
        envInfo: getEnvInfo(),
      };
    },
    computed: {
      envClass() {
        const envMap = {
          test: 'env-test',
          uat: 'env-uat',
          prod: 'env-prod',
          development: 'env-dev',
        };
        return envMap[this.envInfo.environment] || 'env-unknown';
      },
    },
    methods: {
      showEnvInfo() {
        console.log('环境配置详情:', this.envInfo);
        console.log('完整环境对象:', env);
        alert(
          `当前环境: ${this.envInfo.environment}\nAPI地址: ${this.envInfo.apiUrl}`
        );
      },
      checkFeature(featureName) {
        const enabled = isFeatureEnabled(featureName);
        alert(`功能 "${featureName}" ${enabled ? '已启用' : '已禁用'}`);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .env-info {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin: 20px 0;

    h3 {
      margin-top: 0;
      color: #333;
    }

    .env-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
    }

    .env-item {
      display: flex;
      justify-content: space-between;
      padding: 8px;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #eee;

      .label {
        font-weight: bold;
        color: #666;
      }

      .value {
        color: #333;

        &.env-test {
          color: #ff6b35;
          font-weight: bold;
        }

        &.env-uat {
          color: #f7931e;
          font-weight: bold;
        }

        &.env-prod {
          color: #28a745;
          font-weight: bold;
        }

        &.env-dev {
          color: #007bff;
          font-weight: bold;
        }

        &.debug-on {
          color: #dc3545;
          font-weight: bold;
        }
      }
    }

    .feature-flags {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #333;
      }

      .flags-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 8px;
      }

      .flag-item {
        display: flex;
        justify-content: space-between;
        padding: 6px 10px;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #eee;

        &.enabled {
          background-color: #d4edda;
          border-color: #c3e6cb;

          .flag-status {
            color: #155724;
          }
        }

        .flag-name {
          font-weight: bold;
          color: #333;
        }

        .flag-status {
          color: #6c757d;
        }
      }
    }

    .env-actions {
      margin-top: 20px;
      display: flex;
      gap: 10px;

      .btn {
        padding: 8px 16px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          background-color: #0056b3;
        }
      }
    }
  }
</style>
