import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue2';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用@use替代@import以避免弃用警告
        // 需要创建一个空的样式文件来导入变量
        additionalData: `@use "@/assets/styles/variables.scss" as *;`,
      },
      less: {
        additionalData: '@import "@/assets/styles/variables.less";',
      },
    },
  },
  esbuild: {
    target: 'esnext',
  },
});
