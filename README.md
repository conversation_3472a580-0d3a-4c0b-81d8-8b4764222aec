# Vue2 + Vite + TypeScript 项目

这是一个使用 Vue2、Vite、TypeScript、ESLint、Stylelint 和 Prettier 的现代化前端项目。

## 技术栈

- **Vue 2.7.16** - 渐进式 JavaScript 框架
- **Vite 6.0.0** - 下一代前端构建工具
- **TypeScript 5.7.2** - JavaScript 的超集，添加了类型系统
- **ESLint 9.32.0** - JavaScript/TypeScript 代码检查工具
- **Stylelint 16.23.0** - CSS/SCSS 代码检查工具
- **Prettier 3.0.0** - 代码格式化工具

## 项目特性

### ✅ TypeScript 支持
- 完整的 TypeScript 配置
- Vue 单文件组件的 TypeScript 支持
- 类型安全的开发体验
- 路径别名支持 (`@` 指向 `src`)

### ✅ 代码质量工具
- ESLint 配置支持 JavaScript、TypeScript、Vue 文件
- Stylelint 配置支持 CSS、SCSS 文件
- Prettier 代码格式化
- 自动修复功能

### ✅ 开发工具
- 热模块替换 (HMR)
- 快速构建
- 多环境支持 (开发、测试、UAT、生产)

## 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建项目
```bash
npm run build
```

### 代码检查
```bash
# ESLint 检查
npm run lint

# ESLint 自动修复
npm run lint:fix

# Stylelint 检查
npm run stylelint

# Stylelint 自动修复
npm run stylelint:fix

# 代码格式化
npm run prettier

# 完整格式化
npm run format
```

### 类型检查
```bash
npm run type-check
```

## 项目结构

```
vue2-vite/
├── src/
│   ├── components/          # Vue 组件
│   ├── assets/             # 静态资源
│   ├── utils/              # 工具函数
│   ├── config/             # 配置文件
│   ├── App.vue             # 根组件
│   ├── main.js             # 入口文件
│   └── global.d.ts         # 全局类型声明
├── public/                 # 公共资源
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # TypeScript 配置
├── eslint.config.js        # ESLint 配置
├── package.json            # 项目配置
└── README.md              # 项目说明
```

## TypeScript 使用指南

### 创建 TypeScript Vue 组件

```typescript
<template>
  <div>
    <h1>{{ title }}</h1>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';

interface ComponentData {
  title: string;
}

export default Vue.extend({
  name: 'MyComponent',
  data(): ComponentData {
    return {
      title: 'Hello TypeScript!'
    };
  },
  mounted(): void {
    console.log('Component mounted');
  }
});
</script>
```

### 使用类型定义

```typescript
// src/utils/types.ts
export interface User {
  id: number;
  name: string;
  email: string;
}

// 在组件中使用
import { User } from '@/utils/types';

export default Vue.extend({
  data() {
    return {
      user: null as User | null
    };
  }
});
```

## 环境变量

项目支持环境变量配置：

```bash
# .env
VITE_APP_TITLE=Vue2 TypeScript App
VITE_API_BASE_URL=/api
```

## 多环境支持

项目支持多环境构建：

```bash
# 开发环境
npm run dev

# 测试环境
npm run dev:test

# UAT 环境
npm run dev:uat

# 生产环境
npm run dev:prod
```

## 注意事项

1. **Vue 2 兼容性**: 使用 `Vue.extend()` 而不是装饰器语法，确保与 Vue 2 兼容
2. **类型安全**: 所有新代码都应该使用 TypeScript
3. **渐进式迁移**: 可以逐步将现有 `.js` 文件重命名为 `.ts`
4. **类型声明**: 在 `src/global.d.ts` 中添加全局类型声明

## 文件扩展名

- `.ts`: TypeScript 文件
- `.tsx`: TypeScript React JSX 文件（如果需要）
- `.vue`: Vue 单文件组件（使用 `<script lang="ts">`）

## 路径别名

配置了 `@` 别名指向 `src` 目录：

```typescript
// 可以使用
import { User } from '@/utils/types';

// 而不是
import { User } from '../../utils/types';
```

## 许可证

MIT
