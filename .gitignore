# Dependencies
node_modules/

# Build outputs
dist/
dist-ssr/
*.local

# Environment variables
.env
.env.local
.env.*.local
!.env.test
!.env.uat
!.env.prod

# Editor files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache
.cache
.vite/
.eslintcache
.stylelintcache

# Coverage
coverage/
