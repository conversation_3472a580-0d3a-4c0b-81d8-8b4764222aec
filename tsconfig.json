{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node"]}, "vueCompilerOptions": {"globalTypesPath": "./src/global.d.ts"}, "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "vite.config.ts"], "exclude": ["node_modules", "dist"]}