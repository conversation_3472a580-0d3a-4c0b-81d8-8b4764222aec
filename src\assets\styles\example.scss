// SCSS 变量定义
$primary-color: #007bff;
$secondary-color: #6c757d;
$font-family-base: 'Arial', sans-serif;
$border-radius: 4px;

// 混合器 (Mixin)
@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  padding: 10px 20px;
  border: none;
  border-radius: $border-radius;
  cursor: pointer;
  font-family: $font-family-base;

  &:hover {
    opacity: 0.8;
  }
}

// 嵌套规则
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;

  .header {
    background-color: $primary-color;
    color: white;
    padding: 1rem 0;

    h1 {
      margin: 0;
      font-size: 2rem;
    }

    .nav {
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;

        li {
          margin-right: 1rem;

          a {
            color: white;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .content {
    padding: 2rem 0;

    .card {
      background: white;
      border: 1px solid #ddd;
      border-radius: $border-radius;
      padding: 1rem;
      margin-bottom: 1rem;

      .card-title {
        color: $primary-color;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }

      .card-body {
        color: $secondary-color;
      }
    }
  }
}

// 按钮样式
.btn {
  @include button-style($primary-color);

  &.btn-secondary {
    @include button-style($secondary-color);
  }

  &.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .header {
      .nav ul {
        flex-direction: column;

        li {
          margin-right: 0;
          margin-bottom: 0.5rem;
        }
      }
    }
  }
}
