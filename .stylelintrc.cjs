module.exports = {
  extends: ['stylelint-config-standard-scss', 'stylelint-prettier/recommended'],
  plugins: ['stylelint-prettier'],
  rules: {
    // Prettier 集成
    'prettier/prettier': true,

    // 禁用一些过于严格的规则
    'declaration-empty-line-before': null,
    'color-function-alias-notation': null,
    'color-function-notation': null,
    'alpha-value-notation': null,
    'value-keyword-case': null,
    'rule-empty-line-before': [
      'always',
      {
        ignore: ['after-comment', 'first-nested'],
      },
    ],
    'color-hex-length': null,
    'media-feature-range-notation': null,
    'no-descending-specificity': null,

    // SCSS 特定规则
    'scss/at-rule-no-unknown': true,
    'scss/selector-no-redundant-nesting-selector': true,
    'scss/no-global-function-names': true,

    // 允许 SCSS 变量
    'scss/dollar-variable-pattern': null,

    // 允许 SCSS 混合器
    'scss/at-mixin-pattern': null,

    // 允许 SCSS 函数
    'scss/function-quote-no-quoted-strings-inside': null,

    // 允许 SCSS 注释
    'scss/comment-no-empty': null,

    // 允许 SCSS 导入
    'scss/at-import-partial-extension': null,

    // 允许 SCSS 变量声明
    'scss/dollar-variable-colon-space-after': 'always',
    'scss/dollar-variable-colon-space-before': 'never',

    // 允许 SCSS 混合器调用
    'scss/at-mixin-argumentless-call-parentheses': null,

    // 允许 SCSS 嵌套
    'scss/selector-no-union-class-name': true,

    // 允许 SCSS 变量使用
    'scss/dollar-variable-no-missing-interpolation': true,

    // 允许 SCSS 函数
    'scss/function-color-relative': true,
    'scss/function-no-unknown': true,

    // 允许 SCSS 操作符
    'scss/operator-no-newline-after': true,
    'scss/operator-no-newline-before': true,
    'scss/operator-no-unspaced': true,

    // 允许 SCSS 部分文件
    'scss/partial-no-import': null,

    // 允许 SCSS 变量
    'scss/dollar-variable-default': null,

    // 允许 SCSS 注释
    'scss/comment-no-loud': null,
  },
  overrides: [
    {
      files: ['**/*.vue'],
      customSyntax: 'postcss-html',
    },
  ],
};
