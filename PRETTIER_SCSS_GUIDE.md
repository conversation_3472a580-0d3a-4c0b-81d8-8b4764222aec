# Prettier SCSS 配置指南

## 概述

Prettier 对 SCSS 有很好的支持，可以通过多种方式配置来格式化 SCSS 文件。本指南将介绍如何设置和配置 Prettier 来处理 SCSS 文件。

## 安装依赖

### 1. 基础依赖

```bash
npm install prettier@^3.0.0 --save-dev
```

### 2. Stylelint 集成（推荐）

```bash
npm install stylelint stylelint-prettier stylelint-config-standard-scss postcss-html --save-dev
```

## 配置文件

### 1. Prettier 配置 (.prettierrc.cjs)

```javascript
module.exports = {
  // 基本配置
  semi: true,                    // 在语句末尾添加分号
  trailingComma: 'es5',          // 在对象和数组末尾添加逗号
  singleQuote: true,             // 使用单引号
  printWidth: 80,                // 每行最大长度
  tabWidth: 2,                   // 缩进宽度
  useTabs: false,                // 使用空格而不是制表符
  endOfLine: 'auto',             // 行尾换行符

  // Vue特定配置
  vueIndentScriptAndStyle: true, // Vue 文件中的 script 和 style 标签内容缩进

  // 对象和数组配置
  bracketSpacing: true,          // 对象字面量中的括号前后添加空格
  bracketSameLine: false,        // 将多行 JSX 元素的闭合标签放在新行
  arrowParens: 'avoid',          // 箭头函数参数周围省略括号

  // 字符串配置
  quoteProps: 'as-needed',       // 仅在必要时为对象属性添加引号
  jsxSingleQuote: true,          // JSX 中使用单引号

  // SCSS/CSS 相关配置
  htmlWhitespaceSensitivity: 'css', // CSS 空格敏感度，影响 HTML 中的空白处理
  embeddedLanguageFormatting: 'auto', // 自动格式化嵌入的语言（如 Vue 中的 SCSS）

  // 其他配置
  proseWrap: 'preserve',         // 保持 markdown 等文本的换行
};
```

### 2. Stylelint 配置 (.stylelintrc.cjs)

```javascript
module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-prettier/recommended',
  ],
  plugins: ['stylelint-prettier'],
  rules: {
    // Prettier 集成
    'prettier/prettier': true,
    
    // 禁用一些过于严格的规则
    'declaration-empty-line-before': null,
    'color-function-alias-notation': null,
    'color-function-notation': null,
    'alpha-value-notation': null,
    'value-keyword-case': null,
    'rule-empty-line-before': null,
    'color-hex-length': null,
    'media-feature-range-notation': null,
    'no-descending-specificity': null,
    
    // SCSS 特定规则
    'scss/at-rule-no-unknown': true,
    'scss/selector-no-redundant-nesting-selector': true,
    'scss/no-global-function-names': true,
    
    // 允许 SCSS 变量
    'scss/dollar-variable-pattern': null,
    'scss/dollar-variable-colon-space-after': 'always',
    'scss/dollar-variable-colon-space-before': 'never',
    
    // 允许 SCSS 混合器
    'scss/at-mixin-pattern': null,
    'scss/at-mixin-argumentless-call-parentheses': null,
    
    // 允许 SCSS 函数
    'scss/function-quote-no-quoted-strings-inside': null,
    'scss/function-color-relative': true,
    'scss/function-no-unknown': true,
    
    // 允许 SCSS 注释
    'scss/comment-no-empty': null,
    'scss/comment-no-loud': null,
    
    // 允许 SCSS 导入
    'scss/at-import-partial-extension': null,
    
    // 允许 SCSS 嵌套
    'scss/selector-no-union-class-name': true,
    
    // 允许 SCSS 变量使用
    'scss/dollar-variable-no-missing-interpolation': true,
    
    // 允许 SCSS 操作符
    'scss/operator-no-newline-after': true,
    'scss/operator-no-newline-before': true,
    'scss/operator-no-unspaced': true,
    
    // 允许 SCSS 部分文件
    'scss/partial-no-import': null,
    
    // 允许 SCSS 变量
    'scss/dollar-variable-default': null,
  },
  overrides: [
    {
      files: ['**/*.vue'],
      customSyntax: 'postcss-html',
    },
  ],
};
```

### 3. 忽略文件配置

#### .prettierignore
```
# 依赖目录
node_modules/
dist/
build/

# 编译输出
*.min.js
*.min.css

# 其他
.git/
.vscode/
.idea/
*.log
.DS_Store
Thumbs.db

# 特定文件类型
*.md
*.txt
*.json
*.lock
```

#### .stylelintignore
```
# 依赖目录
node_modules/
dist/
build/

# 编译输出
*.min.css
*.min.scss

# 其他
.git/
.vscode/
.idea/
*.log
.DS_Store
Thumbs.db

# 特定文件
public/
```

## Package.json 脚本配置

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .js,.vue,.jsx",
    "lint:fix": "eslint . --ext .js,.vue,.jsx --fix",
    "prettier": "prettier --write ./**/*.{js,vue,jsx,scss}",
    "stylelint": "stylelint \"**/*.{css,scss,vue}\"",
    "stylelint:fix": "stylelint \"**/*.{css,scss,vue}\" --fix",
    "format": "npm run lint:fix && npm run stylelint:fix && npm run prettier"
  }
}
```

## 使用方法

### 1. 格式化所有文件
```bash
npm run format
```

### 2. 仅格式化 SCSS 文件
```bash
npm run prettier
```

### 3. 检查 SCSS 样式
```bash
npm run stylelint
```

### 4. 自动修复 SCSS 样式问题
```bash
npm run stylelint:fix
```

## SCSS 格式化示例

### 格式化前
```scss
$primary-color:#007bff;
$secondary-color:#6c757d;
$font-family-base:'Arial',sans-serif;
$border-radius:4px;

@mixin button-style($bg-color,$text-color:white){
background-color:$bg-color;
color:$text-color;
padding:10px 20px;
border:none;
border-radius:$border-radius;
cursor:pointer;
font-family:$font-family-base;
&:hover{opacity:0.8;}
}

.container{max-width:1200px;margin:0 auto;padding:0 15px;
.header{background-color:$primary-color;color:white;padding:1rem 0;
h1{margin:0;font-size:2rem;}
.nav{ul{list-style:none;padding:0;margin:0;display:flex;
li{margin-right:1rem;
a{color:white;text-decoration:none;
&:hover{text-decoration:underline;}}}}}}}
```

### 格式化后
```scss
// SCSS 变量定义
$primary-color: #007bff;
$secondary-color: #6c757d;
$font-family-base: 'Arial', sans-serif;
$border-radius: 4px;

// 混合器 (Mixin)
@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  padding: 10px 20px;
  border: none;
  border-radius: $border-radius;
  cursor: pointer;
  font-family: $font-family-base;

  &:hover {
    opacity: 0.8;
  }
}

// 嵌套规则
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;

  .header {
    background-color: $primary-color;
    color: white;
    padding: 1rem 0;

    h1 {
      margin: 0;
      font-size: 2rem;
    }

    .nav {
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;

        li {
          margin-right: 1rem;

          a {
            color: white;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
```

## 主要特性

1. **自动格式化**: 自动格式化 SCSS 变量、混合器、嵌套规则等
2. **语法检查**: 通过 Stylelint 检查 SCSS 语法错误
3. **代码风格统一**: 确保团队代码风格一致
4. **Vue 文件支持**: 支持 Vue 单文件组件中的 SCSS
5. **可配置性**: 可以根据项目需求自定义规则

## 注意事项

1. **版本兼容性**: 确保 Prettier 和 Stylelint 版本兼容
2. **编辑器集成**: 建议在编辑器中安装 Prettier 和 Stylelint 插件
3. **Git Hooks**: 可以配置 pre-commit hooks 自动格式化代码
4. **团队协作**: 确保团队成员都使用相同的配置

## 常见问题

### Q: Prettier 和 Stylelint 有什么区别？
A: Prettier 专注于代码格式化，Stylelint 专注于代码质量检查。两者结合使用效果最佳。

### Q: 如何处理 SCSS 变量和混合器？
A: Prettier 会自动格式化 SCSS 变量声明和混合器调用，保持一致的代码风格。

### Q: 支持哪些 SCSS 特性？
A: 支持变量、混合器、嵌套、函数、导入等所有 SCSS 特性。

### Q: 如何自定义格式化规则？
A: 可以通过修改 `.prettierrc.cjs` 和 `.stylelintrc.cjs` 文件来自定义规则。 