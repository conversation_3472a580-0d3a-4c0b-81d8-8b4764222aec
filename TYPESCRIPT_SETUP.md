# TypeScript 配置说明

本项目已配置支持 TypeScript，包含以下特性：

## 已安装的依赖

### TypeScript 相关
- `typescript`: TypeScript 编译器
- `vue-tsc`: Vue 的 TypeScript 编译器
- `@types/node`: Node.js 类型定义
- `@typescript-eslint/eslint-plugin`: TypeScript ESLint 插件
- `@typescript-eslint/parser`: TypeScript ESLint 解析器

## 配置文件

### 1. tsconfig.json
- 配置了 TypeScript 编译选项
- 支持 Vue 单文件组件
- 配置了路径别名 `@` 指向 `src` 目录
- 包含严格模式检查

### 2. vite.config.ts
- 将配置文件转换为 TypeScript
- 添加了文件扩展名解析
- 配置了 esbuild 目标

### 3. eslint.config.js
- 添加了 TypeScript 支持
- 配置了 TypeScript 特定的 ESLint 规则
- 支持 `.ts` 和 `.tsx` 文件检查

### 4. src/global.d.ts
- 全局类型声明文件
- 声明了 Vue 组件、图片文件等模块类型
- 定义了环境变量类型

## 使用方法

### 1. 创建 TypeScript Vue 组件

```typescript
<template>
  <div>
    <h1>{{ title }}</h1>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';

interface ComponentData {
  title: string;
}

export default Vue.extend({
  name: 'MyComponent',
  data(): ComponentData {
    return {
      title: 'Hello TypeScript!'
    };
  },
  mounted(): void {
    console.log('Component mounted');
  }
});
</script>
```

### 2. 使用类型定义

```typescript
// src/utils/types.ts
export interface User {
  id: number;
  name: string;
  email: string;
}

// 在组件中使用
import { User } from '@/utils/types';

export default Vue.extend({
  data() {
    return {
      user: null as User | null
    };
  }
});
```

### 3. 可用的脚本命令

```bash
# 开发模式
npm run dev

# 类型检查
npm run type-check

# 构建（包含类型检查）
npm run build

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 注意事项

1. **Vue 2 兼容性**: 使用 `Vue.extend()` 而不是装饰器语法，确保与 Vue 2 兼容
2. **类型安全**: 所有新代码都应该使用 TypeScript
3. **渐进式迁移**: 可以逐步将现有 `.js` 文件重命名为 `.ts`
4. **类型声明**: 在 `src/global.d.ts` 中添加全局类型声明

## 文件扩展名

- `.ts`: TypeScript 文件
- `.tsx`: TypeScript React JSX 文件（如果需要）
- `.vue`: Vue 单文件组件（使用 `<script lang="ts">`）

## 环境变量类型

在 `src/global.d.ts` 中定义了环境变量类型：

```typescript
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_BASE_URL: string;
  readonly MODE: string;
  readonly DEV: boolean;
  readonly PROD: boolean;
}
```

## 路径别名

配置了 `@` 别名指向 `src` 目录：

```typescript
// 可以使用
import { User } from '@/utils/types';

// 而不是
import { User } from '../../utils/types';
``` 