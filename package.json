{"name": "vue2-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:uat": "vite --mode uat", "dev:prod": "vite --mode prod", "build": "vue-tsc && vite build", "build:test": "vue-tsc && vite build --mode test", "build:uat": "vue-tsc && vite build --mode uat", "build:prod": "vue-tsc && vite build --mode prod", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:uat": "vite preview --mode uat", "preview:prod": "vite preview --mode prod", "lint": "eslint . --ext .js,.vue,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.vue,.jsx,.ts,.tsx --fix", "prettier": "prettier --write ./**/*.{js,vue,jsx,ts,tsx,scss}", "stylelint": "stylelint \"**/*.{css,scss,vue}\"", "stylelint:fix": "stylelint \"**/*.{css,scss,vue}\" --fix", "format": "npm run lint:fix && npm run stylelint:fix && npm run prettier", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^2.7.16"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitejs/plugin-vue2": "^2.3.1", "eslint": "^9.32.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^10.4.0", "less": "^4.4.0", "postcss-html": "^1.8.0", "prettier": "^3.0.0", "sass": "^1.89.2", "stylelint": "^16.23.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-prettier": "^5.0.3", "typescript": "^5.7.2", "vite": "^6.0.0", "vue-tsc": "^2.0.7", "vue-property-decorator": "^9.1.2"}}